<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NCast - Favourite Podcasts</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #ffffff;
            color: #1f1f1f;
        }

        .container {
            width: 428px;
            height: 926px;
            margin: 0 auto;
            background-color: #ffffff;
            position: relative;
            overflow: hidden;
        }

        /* 组件样式 */
        .component {
            position: absolute;
        }

        /* 组件1: Group 147 - 通知按钮 */
        #component-1 {
            top: 48px;
            right: 32px;
            width: 44px;
            height: 44px;
        }

        .notification-btn {
            position: relative;
            width: 44px;
            height: 44px;
            background-color: rgba(31, 31, 31, 0.1);
            border-radius: 22px;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }

        .notification-badge {
            position: absolute;
            top: 2px;
            right: 4px;
            width: 9px;
            height: 9px;
            background-color: #ff5757;
            border-radius: 4.5px;
        }

        .bell-icon {
            width: 17px;
            height: 17px;
        }

        /* 组件2: Favourite Podcasts 标题 */
        #component-2 {
            top: 132px;
            left: 32px;
            width: 219px;
            height: 28px;
        }

        .title {
            font-size: 24px;
            font-weight: 500;
            color: #1f1f1f;
        }

        /* 播客列表项样式 */
        .podcast-item {
            display: flex;
            align-items: center;
            gap: 16px;
            width: 364px;
            height: 96px;
        }

        .podcast-cover {
            width: 102px;
            height: 90px;
            border-radius: 12px;
            object-fit: cover;
        }

        .podcast-info {
            flex: 1;
            line-height: 1.4;
        }

        .podcast-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #1f1f1f;
        }

        .podcast-category {
            font-size: 13px;
            color: rgba(31, 31, 31, 0.7);
            margin-bottom: 10px;
        }

        .podcast-duration {
            font-size: 13px;
            color: rgba(31, 31, 31, 0.7);
        }

        .play-btn {
            width: 40px;
            height: 40px;
            background-color: rgba(76, 0, 153, 0.1);
            border-radius: 20px;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            flex-shrink: 0;
        }

        .play-icon {
            width: 14px;
            height: 14px;
        }

        /* 组件3-8: 播客列表项位置 (根据Figma坐标调整) */
        #component-3 { top: 184px; left: 32px; }
        #component-4 { top: 306px; left: 32px; }
        #component-5 { top: 428px; left: 32px; }
        #component-6 { top: 550px; left: 32px; }
        #component-7 { top: 672px; left: 32px; }
        #component-8 { top: 794px; left: 32px; }

        /* 组件9: Rectangle 3010 - 渐变背景 */
        #component-9 {
            top: 770px;
            left: 0;
            width: 428px;
            height: 156px;
            background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 1) 100%);
        }

        /* 组件10: Group 178 - 底部导航 */
        #component-10 {
            top: 830px;
            left: 32px;
            width: 364px;
            height: 64px;
        }

        .nav-container {
            width: 364px;
            height: 64px;
            background-color: rgba(76, 0, 153, 0.15);
            border-radius: 32px;
            display: flex;
            align-items: center;
            justify-content: space-around;
            position: relative;
            padding: 0 20px;
        }

        .nav-item {
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            opacity: 0.5;
        }

        .nav-item.active {
            opacity: 1;
        }

        .nav-item img {
            width: 28px;
            height: 28px;
        }

        .nav-dot {
            position: absolute;
            bottom: 16px;
            left: 50%;
            transform: translateX(-50%);
            width: 5px;
            height: 5px;
            background-color: #4c0099;
            border-radius: 50%;
        }

        /* 组件11: Group 179 - Logo */
        #component-11 {
            top: 48px;
            left: 32px;
            width: 350px;
            height: 46px;
        }

        .logo-container {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .logo-icon {
            width: 50px;
            height: 46px;
        }

        .logo-text {
            height: 30px;
            width: auto;
            max-width: 200px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 组件11: Group 179 - Logo -->
        <div id="component-11" class="component">
            <div class="logo-container">
                <img src="images/logo-icon.png" alt="NCast Logo" class="logo-icon">
                <img src="images/ncast-logo.png" alt="NCAST" class="logo-text">
            </div>
        </div>

        <!-- 组件1: Group 147 - 通知按钮 -->
        <div id="component-1" class="component">
            <button class="notification-btn">
                <img src="images/notification-icon.png" alt="Notifications" class="bell-icon">
                <div class="notification-badge"></div>
            </button>
        </div>

        <!-- 组件2: Favourite Podcasts 标题 -->
        <div id="component-2" class="component">
            <h1 class="title">Favourite Podcasts</h1>
        </div>

        <!-- 组件3: Group 158 - 播客项1 -->
        <div id="component-3" class="component">
            <div class="podcast-item">
                <img src="images/podcast-1.png" alt="Sunday Summer - Ep3" class="podcast-cover">
                <div class="podcast-info">
                    <div class="podcast-title">Sunday Summer - Ep3</div>
                    <div class="podcast-category">Entertainment</div>
                    <div class="podcast-duration">15 min</div>
                </div>
                <button class="play-btn">
                    <img src="images/play-button-1.png" alt="Play" class="play-icon">
                </button>
            </div>
        </div>

        <!-- 组件4: Group 159 - 播客项2 -->
        <div id="component-4" class="component">
            <div class="podcast-item">
                <img src="images/podcast-2.png" alt="Musical Soul - Vol. 1" class="podcast-cover">
                <div class="podcast-info">
                    <div class="podcast-title">Musical Soul - Vol. 1</div>
                    <div class="podcast-category">Lifestyle</div>
                    <div class="podcast-duration">35 min</div>
                </div>
                <button class="play-btn">
                    <img src="images/play-button-2.png" alt="Play" class="play-icon">
                </button>
            </div>
        </div>

        <!-- 组件5: Group 160 - 播客项3 -->
        <div id="component-5" class="component">
            <div class="podcast-item">
                <img src="images/podcast-3.png" alt="Talk Show - Ep4" class="podcast-cover">
                <div class="podcast-info">
                    <div class="podcast-title">Talk Show - Ep4</div>
                    <div class="podcast-category">Business</div>
                    <div class="podcast-duration">20 min</div>
                </div>
                <button class="play-btn">
                    <img src="images/play-button-3.png" alt="Play" class="play-icon">
                </button>
            </div>
        </div>

        <!-- 组件6: Group 161 - 播客项4 -->
        <div id="component-6" class="component">
            <div class="podcast-item">
                <img src="images/podcast-4.png" alt="Musical Soul - Vol. 2" class="podcast-cover">
                <div class="podcast-info">
                    <div class="podcast-title">Musical Soul - Vol. 2</div>
                    <div class="podcast-category">Lifestyle</div>
                    <div class="podcast-duration">30 min</div>
                </div>
                <button class="play-btn">
                    <img src="images/play-button-4.png" alt="Play" class="play-icon">
                </button>
            </div>
        </div>

        <!-- 组件7: Group 162 - 播客项5 -->
        <div id="component-7" class="component">
            <div class="podcast-item">
                <img src="images/podcast-5.png" alt="Unravelling The Mind" class="podcast-cover">
                <div class="podcast-info">
                    <div class="podcast-title">Unravelling The Mind</div>
                    <div class="podcast-category">Healthy Lifestyle</div>
                    <div class="podcast-duration">10 min</div>
                </div>
                <button class="play-btn">
                    <img src="images/play-button-5.png" alt="Play" class="play-icon">
                </button>
            </div>
        </div>

        <!-- 组件8: Group 163 - 播客项6 -->
        <div id="component-8" class="component">
            <div class="podcast-item">
                <img src="images/podcast-6.png" alt="Talk Show - Ep8" class="podcast-cover">
                <div class="podcast-info">
                    <div class="podcast-title">Talk Show - Ep8</div>
                    <div class="podcast-category">Entertainment</div>
                    <div class="podcast-duration">20 min</div>
                </div>
                <button class="play-btn">
                    <img src="images/play-button-6.png" alt="Play" class="play-icon">
                </button>
            </div>
        </div>

        <!-- 组件9: Rectangle 3010 - 渐变背景 -->
        <div id="component-9" class="component"></div>

        <!-- 组件10: Group 178 - 底部导航 -->
        <div id="component-10" class="component">
            <div class="nav-container">
                <div class="nav-item">
                    <img src="images/headphones-icon.png" alt="Headphones">
                </div>
                <div class="nav-item">
                    <img src="images/compass-icon.png" alt="Compass">
                </div>
                <div class="nav-item active">
                    <img src="images/heart-icon.png" alt="Heart">
                </div>
                <div class="nav-item">
                    <img src="images/menu-icon.png" alt="Menu">
                </div>
                <div class="nav-dot"></div>
            </div>
        </div>
    </div>
</body>
</html>
